"use client"

export type Department = {
  id: string
  name: string
}

export type Manager = {
  id: string // This would be the Clerk user_id
  fullName: string
}

export type Employee = {
  id: string
  fullName: string
  compensation: "hourly" | "monthly"
  rate: number
  departmentId: string
  departmentName?: string
  managerId?: string | null
  managerName?: string | null
  active: boolean
}

export type AppraisalPeriod = {
  id: string
  periodStart: string // ISO date string
  periodEnd: string // ISO date string
  closed: boolean
}

export type AppraisalStatus = "submitted" | "draft" | "not-started"

export type EmployeeAppraisal = {
  employeeId: string
  fullName: string
  departmentName: string
  status: AppraisalStatus
  submittedAt?: string
}

export type UserRole = "super-admin" | "hr-admin" | "admin" | "manager" | "accountant"

export type MockUser = {
  id: string
  fullName: string
  role: UserRole
}

export type UserSession = {
  id: string
  full_name: string
  role: UserRole
}

export type AppraisalDetails = {
  id: string
  periodId: string
  employeeId: string
  managerId: string
  q1: "below-expectations" | "meets-expectations" | "exceeds-expectations" | null
  q2: boolean
  q3: string
  q4: string
  q5: string
  status: "draft" | "submitted"
}

export type EmployeeDetails = {
  id: string
  fullName: string
  departmentName: string
  compensation: "hourly" | "monthly"
  rate: number
}

export type AccountingViewData = {
  employeeId: string
  employeeName: string
  departmentName: string
  managerName: string
  status: AppraisalStatus
  submittedAt: string | null
  compensation: "hourly" | "monthly"
  rate: number
  hours: number
}

export type PerformanceStats = {
  total: number
  belowExpectations: number
  meetsExpectations: number
  exceedsExpectations: number
  notStarted: number
  submittedCount: number
  draftCount: number
}

// PTO (Paid Time Off) Types
export type PTORequestStatus = "pending" | "approved" | "rejected" | "cancelled"
export type PTORequestType = "vacation" | "sick" | "personal" | "emergency"

export type PTOBalance = {
  id: string
  employeeId: string
  year: number
  totalDays: number
  usedDays: number
  availableDays: number
  createdAt: string
  updatedAt: string
}

export type PTORequest = {
  id: string
  employeeId: string
  employeeName: string
  managerId: string
  managerName: string
  requestType: PTORequestType
  startDate: string // ISO date string
  endDate: string // ISO date string
  daysRequested: number
  reason?: string
  status: PTORequestStatus
  approvedBy?: string
  approvedAt?: string
  rejectedReason?: string
  createdAt: string
  updatedAt: string
}

export type PTORequestWithBalance = PTORequest & {
  employeeBalance: PTOBalance
}

export type PTOStats = {
  totalRequests: number
  pendingRequests: number
  approvedRequests: number
  rejectedRequests: number
}

export type PTODashboardData = {
  balance: PTOBalance
  recentRequests: PTORequest[]
  stats: PTOStats
}

// Appraisal Template Types
export type TemplateQuestionType = "text" | "textarea" | "radio" | "checkbox" | "select"

export type TemplateQuestionOption = {
  value: string
  label: string
}

export type TemplateQuestion = {
  id: string
  type: TemplateQuestionType
  question: string
  required: boolean
  placeholder?: string
  options?: TemplateQuestionOption[]
  rows?: number // For textarea
}

export type AppraisalTemplate = {
  id: string
  name: string
  description?: string
  questions: TemplateQuestion[]
  departmentId?: string
  departmentName?: string
  roleFilter?: string
  isActive: boolean
  isDefault: boolean
  version: number
  createdBy: string
  createdAt: string
  updatedAt: string
}

export type TemplateUsage = {
  id: string
  templateId: string
  periodId: string
  managerId: string
  usageCount: number
  createdAt: string
}

// Update AppraisalDetails to include template reference
export type AppraisalDetailsWithTemplate = AppraisalDetails & {
  templateId?: string
  templateName?: string
}

export type PTOManagerDashboardData = {
  pendingRequests: PTORequest[]
  teamRequests: PTORequest[]
  stats: PTOStats
}
