import type {
  EmployeeAppraisal,
  MockUser,
  EmployeeDetails,
  AppraisalDetails,
  AccountingViewData,
  UserRole,
  Employee,
  Department,
  Manager,
  AppraisalPeriod,
  PerformanceStats,
} from "./types"
import { db } from "./db"

// --- SIMPLE CACHING MECHANISM ---
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class SimpleCache {
  private cache = new Map<string, CacheEntry<any>>()
  
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null
    
    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.data
  }
  
  set<T>(key: string, data: T, ttl: number = 60000): void { // Default 1 minute TTL
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  clear(): void {
    this.cache.clear()
  }
  
  delete(key: string): void {
    this.cache.delete(key)
  }
}

const cache = new SimpleCache()

// --- FALLBACK MOCK DATA FOR DEVELOPMENT ---
// These are used when database is not available or for testing
const fallbackMockUsers: Record<UserRole, MockUser> = {
  manager: { id: "a1b2c3d4-e5f6-7890-1234-567890abcdef", fullName: "Francesco", role: "manager" },
  accountant: { id: "b2c3d4e5-f6g7-8901-2345-678901bcdefg", fullName: "Grace", role: "accountant" },
  admin: { id: "tarek_clerk_user_id_placeholder", fullName: "Tarek H", role: "admin" },
  "hr-admin": { id: "c3d4e5f6-g7h8-9012-3456-789012cdefgh", fullName: "Joelle", role: "hr-admin" },
  "super-admin": { id: "bob_clerk_user_id_placeholder", fullName: "Bob Wazneh", role: "super-admin" },
}

// --- DATA ACCESS FUNCTIONS ---
// These functions now use the database instead of mock data

export async function getLoggedInUser(role: UserRole = "manager"): Promise<MockUser> {
  // This function is kept for backward compatibility during migration
  // In production, user data should come from Clerk
  return fallbackMockUsers[role]
}

// Departments
export async function getDepartments(): Promise<Department[]> {
  try {
    const departments = await db.getDepartments()
    return departments.map(dept => ({
      id: dept.id,
      name: dept.name
    }))
  } catch (error) {
    console.error('Failed to fetch departments from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function saveDepartment(dept: Partial<Department>): Promise<void> {
  try {
    if (dept.id) {
      await db.updateDepartment(dept.id, dept.name!)
    } else {
      await db.createDepartment(dept.name!)
    }
  } catch (error) {
    console.error('Failed to save department to database:', error)
    throw new Error('Failed to save department')
  }
}

// Managers
export async function getManagers(): Promise<Manager[]> {
  try {
    const managers = await db.getManagers()
    return managers.map(manager => ({
      id: manager.user_id,
      fullName: manager.full_name
    }))
  } catch (error) {
    console.error('Failed to fetch managers from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

// Employees
export async function getEmployees(): Promise<Employee[]> {
  try {
    const employees = await db.getEmployees()
    return employees.map(emp => ({
      id: emp.id,
      fullName: emp.full_name,
      compensation: emp.rate,
      rate: emp.compensation,
      departmentId: emp.department_id || '',
      departmentName: emp.department_name,
      managerId: emp.manager_id,
      managerName: emp.manager_name,
      active: emp.active
    }))
  } catch (error) {
    console.error('Failed to fetch employees from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function saveEmployee(emp: Partial<Employee>): Promise<void> {
  try {
    if (emp.id) {
      await db.updateEmployee(emp.id, {
        fullName: emp.fullName,
        compensation: emp.rate,
        rate: emp.compensation,
        departmentId: emp.departmentId,
        managerId: emp.managerId || undefined,
        active: emp.active
      })
    } else {
      await db.createEmployee({
        fullName: emp.fullName!,
        compensation: emp.rate!,
        rate: emp.compensation!,
        departmentId: emp.departmentId!,
        managerId: emp.managerId || undefined
      })
    }
  } catch (error) {
    console.error('Failed to save employee to database:', error)
    throw new Error('Failed to save employee')
  }
}

// Periods
export async function getPeriods(): Promise<AppraisalPeriod[]> {
  try {
    const periods = await db.getAppraisalPeriods()
    return periods.map(period => ({
      id: period.id,
      periodStart: period.start_date,
      periodEnd: period.end_date,
      closed: period.status !== 'active'
    }))
  } catch (error) {
    console.error('Failed to fetch periods from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function savePeriod(period: Partial<AppraisalPeriod>): Promise<void> {
  try {
    if (period.id) {
      await db.updateAppraisalPeriod(period.id, {
        startDate: period.periodStart,
        endDate: period.periodEnd,
        status: period.closed ? 'closed' : 'active'
      })
    } else {
      await db.createAppraisalPeriod({
        name: `Period ${new Date().getFullYear()}`,
        startDate: period.periodStart!,
        endDate: period.periodEnd!,
        status: period.closed ? 'closed' : 'active'
      })
    }
  } catch (error) {
    console.error('Failed to save period to database:', error)
    throw new Error('Failed to save period')
  }
}

// --- DATABASE-BACKED FUNCTIONS ---
// These functions now use real database queries

export async function getManagerAppraisals(): Promise<EmployeeAppraisal[]> {
  try {
    const { getCurrentUser, hasSuperAdminAccess } = await import('./auth')
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }

    console.log('🔍 [DEBUG] getManagerAppraisals - Current user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    const employees = await getEmployees()

    console.log('👥 [DEBUG] getManagerAppraisals - All employees:', employees.map(emp => ({
      id: emp.id,
      fullName: emp.fullName,
      managerId: emp.managerId,
      managerName: emp.managerName
    })))

    // Super-admins see all employees, managers see only their managed employees
    const managedEmployees = hasSuperAdminAccess(currentUser)
      ? employees
      : employees.filter(emp => emp.managerId === currentUser.id)

    console.log('🎯 [DEBUG] getManagerAppraisals - Managed employees:', managedEmployees.map(emp => ({
      id: emp.id,
      fullName: emp.fullName,
      managerId: emp.managerId,
      managerName: emp.managerName
    })))

    console.log(`📊 [DEBUG] getManagerAppraisals - Found ${managedEmployees.length} employees managed by ${currentUser.fullName} (ID: ${currentUser.id})`)

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      // Return employees with not-started status if no active period
      return managedEmployees.map((emp) => ({
        employeeId: emp.id,
        fullName: emp.fullName,
        departmentName: emp.departmentName!,
        status: "not-started" as const,
        submittedAt: undefined,
      }))
    }

    // Get appraisals for the current period
    const appraisals = await db.getAppraisalsWithEmployeeData(currentPeriod.id)

    console.log('📋 [DEBUG] getManagerAppraisals - Fetched appraisals:', appraisals.map(appraisal => ({
      employee_id: appraisal.employee_id,
      status: appraisal.status,
      submitted_at: appraisal.submitted_at
    })))

    // Create a map of employee ID to appraisal data
    const appraisalMap = new Map(appraisals.map(appraisal => [appraisal.employee_id, appraisal]))

    // Build the result with real appraisal statuses
    return managedEmployees.map((emp) => {
      const appraisal = appraisalMap.get(emp.id)

      // Map database status to UI status
      let status: 'not-started' | 'draft' | 'submitted' = 'not-started'
      let submittedAt: string | undefined = undefined

      if (appraisal) {
        console.log(`📝 [DEBUG] getManagerAppraisals - Employee ${emp.fullName} appraisal status: ${appraisal.status}`)
        if (appraisal.status === 'submitted') {
          status = 'submitted'
          submittedAt = appraisal.submitted_at || undefined
        } else if (appraisal.status === 'pending') {
          status = 'draft'
        }
      } else {
        console.log(`📝 [DEBUG] getManagerAppraisals - No appraisal found for employee ${emp.fullName}`)
      }

      return {
        employeeId: emp.id,
        fullName: emp.fullName,
        departmentName: emp.departmentName!,
        status,
        submittedAt,
      }
    })
  } catch (error) {
    console.error('Failed to fetch manager appraisals:', error)
    return []
  }
}

export async function getEmployeeDetails(employeeId: string): Promise<EmployeeDetails | null> {
  try {
    const employee = await db.getEmployeeById(employeeId)
    if (!employee) return null

    return {
      id: employee.id,
      fullName: employee.full_name,
      departmentName: employee.department_name || 'N/A',
      compensation: employee.rate as 'hourly' | 'monthly',
      rate: Number(employee.compensation),
    }
  } catch (error) {
    console.error('Failed to fetch employee details:', error)
    return null
  }
}

export async function getPreviousAppraisal(employeeId: string): Promise<AppraisalDetails | null> {
  try {
    // Get all periods sorted by date
    const periods = await getPeriods()
    const sortedPeriods = periods.sort((a, b) => new Date(b.periodStart).getTime() - new Date(a.periodStart).getTime())
    
    // Find the current period
    const currentPeriod = sortedPeriods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return null
    }
    
    // Find the previous period (the most recent closed period)
    const previousPeriod = sortedPeriods.find(p => p.closed && new Date(p.periodStart) < new Date(currentPeriod.periodStart))
    
    if (!previousPeriod) {
      // console.log('No previous appraisal period found')
      return null
    }
    
    // Get the appraisal for the previous period
    const previousAppraisal = await db.getAppraisalByEmployeeId(employeeId, previousPeriod.id)
    
    if (!previousAppraisal) {
      // console.log('No previous appraisal found for employee')
      return null
    }
    
    return {
      id: previousAppraisal.id,
      periodId: previousAppraisal.period_id,
      employeeId: previousAppraisal.employee_id,
      managerId: previousAppraisal.manager_id,
      q1: previousAppraisal.question_1 as 'below-expectations' | 'meets-expectations' | 'exceeds-expectations' | null,
      q2: previousAppraisal.question_2 === 'true' || false,
      q3: previousAppraisal.question_3 || '',
      q4: previousAppraisal.question_4 || '',
      q5: previousAppraisal.question_5 || '',
      status: previousAppraisal.status as 'draft' | 'submitted'
    }
  } catch (error) {
    console.error('Failed to fetch previous appraisal:', error)
    return null
  }
}

export async function getAppraisalDetails(employeeId: string): Promise<AppraisalDetails | null> {
  try {
    // Get the current active period (in real app, this would be more sophisticated)
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return null
    }

    const appraisal = await db.getAppraisalByEmployeeId(employeeId, currentPeriod.id)

    if (!appraisal) {
      // No current appraisal exists, try to prefill from previous period
      const previousAppraisal = await getPreviousAppraisal(employeeId)
      
      if (previousAppraisal) {
        // Return a new draft appraisal structure prefilled with previous data
        // console.log('Prefilling appraisal with previous month data')
        return {
          id: '', // Will be generated when saved
          periodId: currentPeriod.id,
          employeeId,
          managerId: '', // Will be set by the current user
          q1: previousAppraisal.q1, // Prefill from previous
          q2: previousAppraisal.q2, // Prefill from previous
          q3: previousAppraisal.q3, // Prefill from previous
          q4: '', // Reset this field as it's month-specific
          q5: '', // Reset this field as it's month-specific
          status: 'draft'
        }
      }
      
      // No previous appraisal found, return empty structure
      return {
        id: '', // Will be generated when saved
        periodId: currentPeriod.id,
        employeeId,
        managerId: '', // Will be set by the current user
        q1: null,
        q2: false,
        q3: '',
        q4: '',
        q5: '',
        status: 'draft'
      }
    }

    return {
      id: appraisal.id,
      periodId: appraisal.period_id,
      employeeId: appraisal.employee_id,
      managerId: appraisal.manager_id,
      q1: appraisal.question_1 as 'below-expectations' | 'meets-expectations' | 'exceeds-expectations' | null,
      q2: appraisal.question_2 === 'true' || false,
      q3: appraisal.question_3 || '',
      q4: appraisal.question_4 || '',
      q5: appraisal.question_5 || '',
      status: appraisal.status as 'draft' | 'submitted'
    }
  } catch (error) {
    console.error('Failed to fetch appraisal details:', error)
    return null
  }
}

export async function getAccountingData(): Promise<AccountingViewData[]> {
  try {
    const employees = await getEmployees()
    // TODO: In real implementation, this would join with appraisals table to get actual status and submission times
    return employees.map((emp) => ({
      employeeId: emp.id,
      employeeName: emp.fullName,
      departmentName: emp.departmentName!,
      managerName: emp.managerName || 'Unassigned',
      status: "not-started" as const, // In real app, this would come from appraisals table
      submittedAt: null, // In real app, this would come from appraisals table
      compensation: emp.compensation,
      rate: emp.rate,
      hours: emp.compensation === "hourly" ? 160 : 160, // Standard work hours
    }))
  } catch (error) {
    console.error('Failed to fetch accounting data:', error)
    return []
  }
}

export async function getAccountingDataForUser(): Promise<AccountingViewData[]> {
  try {
    const { getCurrentUser } = await import('./auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }
    
    // Get current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return []
    }
    
    // Get all employees first
    const employees = await getEmployees()
    
    // Filter based on user role
    let filteredEmployees: Employee[]
    
    if (currentUser.role === 'super-admin') {
      // Super admins see all employees
      filteredEmployees = employees
    } else if (currentUser.role === 'accountant') {
      // Accountants see all employees (for payroll processing)
      filteredEmployees = employees
    } else if (currentUser.role === 'manager') {
      // Managers only see employees under their supervision
      filteredEmployees = employees.filter(emp => emp.managerId === currentUser.id)
    } else if (currentUser.role === 'hr-admin' || currentUser.role === 'admin') {
      // HR admins and admins see all employees
      filteredEmployees = employees
    } else {
      // Default: no access
      filteredEmployees = []
    }
    
    // Get appraisals for the current period
    const appraisals = await db.getAppraisalsWithEmployeeData(currentPeriod.id)
    
    // Create a map of employee ID to appraisal data
    const appraisalMap = new Map(appraisals.map(appraisal => [appraisal.employee_id, appraisal]))
    
    // Build the accounting view data with real appraisal statuses
    return filteredEmployees.map((emp) => {
      const appraisal = appraisalMap.get(emp.id)
      
      // Map database status to UI status
      let status: 'not-started' | 'draft' | 'submitted' = 'not-started'
      let submittedAt: string | null = null
      
      if (appraisal) {
        if (appraisal.status === 'submitted') {
          status = 'submitted'
          submittedAt = appraisal.submitted_at
        } else if (appraisal.status === 'pending') {
          status = 'draft'
        }
      }
      
      return {
        employeeId: emp.id,
        employeeName: emp.fullName,
        departmentName: emp.departmentName!,
        managerName: emp.managerName || 'Unassigned',
        status,
        submittedAt,
        compensation: emp.compensation,
        rate: emp.rate,
        hours: emp.compensation === "hourly" ? 160 : 160, // Standard work hours
      }
    })
  } catch (error) {
    console.error('Failed to fetch accounting data for user:', error)
    return []
  }
}

export async function getPerformanceStats(): Promise<PerformanceStats> {
  try {
    // Check cache first
    const cacheKey = 'global-performance-stats'
    const cachedStats = cache.get<PerformanceStats>(cacheKey)
    if (cachedStats) {
      // console.log('📊 Using cached performance stats')
      return cachedStats
    }

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      const emptyStats = {
        total: 0,
        belowExpectations: 0,
        meetsExpectations: 0,
        exceedsExpectations: 0,
        notStarted: 0,
        submittedCount: 0,
        draftCount: 0,
      }
      // Cache empty stats for a shorter time
      cache.set(cacheKey, emptyStats, 30000) // 30 seconds
      return emptyStats
    }

    // Get all appraisals for the current period
    const appraisals = await db.getPerformanceStatsByPeriod(currentPeriod.id)
    
    // Initialize counters
    let belowExpectations = 0
    let meetsExpectations = 0
    let exceedsExpectations = 0
    let submittedCount = 0
    let draftCount = 0
    
    // Count appraisals by q1 response and status
    appraisals.forEach(appraisal => {
      if (appraisal.status === 'submitted') {
        submittedCount++
        
        // Only count q1 responses for submitted appraisals
        if (appraisal.question_1 === 'below-expectations') {
          belowExpectations++
        } else if (appraisal.question_1 === 'meets-expectations') {
          meetsExpectations++
        } else if (appraisal.question_1 === 'exceeds-expectations') {
          exceedsExpectations++
        }
      } else if (appraisal.status === 'pending') {
        draftCount++
      }
    })
    
    // Get total employees to calculate not started
    const employees = await getEmployees()
    const totalEmployees = employees.length
    const notStarted = totalEmployees - submittedCount - draftCount

    const stats = {
      total: totalEmployees,
      belowExpectations,
      meetsExpectations,
      exceedsExpectations,
      notStarted: Math.max(0, notStarted), // Ensure not negative
      submittedCount,
      draftCount,
    }
    
    // Cache the result for 2 minutes
    cache.set(cacheKey, stats, 120000)
    // console.log('📊 Cached performance stats for 2 minutes')
    
    return stats
  } catch (error) {
    console.error('Failed to fetch performance statistics:', error)
    return {
      total: 0,
      belowExpectations: 0,
      meetsExpectations: 0,
      exceedsExpectations: 0,
      notStarted: 0,
      submittedCount: 0,
      draftCount: 0,
    }
  }
}

export async function getManagerPerformanceStats(): Promise<PerformanceStats> {
  try {
    const { getCurrentUser } = await import('./auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return {
        total: 0,
        belowExpectations: 0,
        meetsExpectations: 0,
        exceedsExpectations: 0,
        notStarted: 0,
        submittedCount: 0,
        draftCount: 0,
      }
    }

    // Check cache first (unique per manager)
    const cacheKey = `manager-performance-stats-${currentUser.id}`
    const cachedStats = cache.get<PerformanceStats>(cacheKey)
    if (cachedStats) {
      // console.log('📊 Using cached manager performance stats')
      return cachedStats
    }

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return {
        total: 0,
        belowExpectations: 0,
        meetsExpectations: 0,
        exceedsExpectations: 0,
        notStarted: 0,
        submittedCount: 0,
        draftCount: 0,
      }
    }

    // Get all appraisals for the current manager and period
    const appraisals = await db.getAllAppraisalsForManager(currentUser.id, currentPeriod.id)
    
    // Initialize counters
    let belowExpectations = 0
    let meetsExpectations = 0
    let exceedsExpectations = 0
    let submittedCount = 0
    let draftCount = 0
    
    // Count appraisals by q1 response and status
    appraisals.forEach(appraisal => {
      if (appraisal.status === 'submitted') {
        submittedCount++
        
        // Only count q1 responses for submitted appraisals
        if (appraisal.question_1 === 'below-expectations') {
          belowExpectations++
        } else if (appraisal.question_1 === 'meets-expectations') {
          meetsExpectations++
        } else if (appraisal.question_1 === 'exceeds-expectations') {
          exceedsExpectations++
        }
      } else if (appraisal.status === 'draft') {
        draftCount++
      }
    })
    
    // Get total employees managed by this manager
    const managedEmployees = await getEmployees()
    const totalManagedEmployees = managedEmployees.filter(emp => emp.managerId === currentUser.id).length
    const notStarted = totalManagedEmployees - submittedCount - draftCount

    const stats = {
      total: totalManagedEmployees,
      belowExpectations,
      meetsExpectations,
      exceedsExpectations,
      notStarted: Math.max(0, notStarted), // Ensure not negative
      submittedCount,
      draftCount,
    }
    
    // Cache the result for 2 minutes
    cache.set(cacheKey, stats, 120000)
    // console.log('📊 Cached manager performance stats for 2 minutes')
    
    return stats
  } catch (error) {
    console.error('Failed to fetch manager performance statistics:', error)
    return {
      total: 0,
      belowExpectations: 0,
      meetsExpectations: 0,
      exceedsExpectations: 0,
      notStarted: 0,
      submittedCount: 0,
      draftCount: 0,
    }
  }
}

// Cache invalidation functions
export function invalidatePerformanceStatsCache(managerId?: string): void {
  // console.log('🗑️ Invalidating performance stats cache')
  
  // Clear global stats cache
  cache.delete('global-performance-stats')
  
  // Clear specific manager cache if provided
  if (managerId) {
    cache.delete(`manager-performance-stats-${managerId}`)
  } else {
    // Clear all manager caches (brute force approach)
    // In a real app, you might want to track active manager IDs
    cache.clear()
  }
}

export function clearAllCache(): void {
  // console.log('🗑️ Clearing all cache')
  cache.clear()
}

// Export cache instance for advanced usage
export { cache }

// --- PTO (PAID TIME OFF) DATA ACCESS FUNCTIONS ---

export async function getPTOBalance(employeeId: string, year?: number): Promise<any> {
  try {
    const balance = await db.getPTOBalance(employeeId, year)
    return {
      id: balance.id,
      employeeId: balance.employee_id,
      year: balance.year,
      totalDays: balance.total_days,
      usedDays: balance.used_days,
      availableDays: balance.available_days,
      createdAt: balance.created_at,
      updatedAt: balance.updated_at
    }
  } catch (error) {
    console.error('Failed to fetch PTO balance:', error)
    // Return default balance if error occurs
    return {
      id: '',
      employeeId: employeeId,
      year: year || new Date().getFullYear(),
      totalDays: 7,
      usedDays: 0,
      availableDays: 7,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }
}

export async function getPTOBalanceForCurrentUser(): Promise<any> {
  try {
    const { getCurrentUser } = await import('./auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      throw new Error('No authenticated user found')
    }
    
    // Get the employee record for the current user
    const employees = await getEmployees()
    const employee = employees.find(emp => emp.managerId === currentUser.id)
    
    if (!employee) {
      throw new Error('No employee record found for current user')
    }
    
    return await getPTOBalance(employee.id)
  } catch (error) {
    console.error('Failed to fetch PTO balance for current user:', error)
    return null
  }
}

export async function getPTORequestsForManager(managerId?: string, status?: string): Promise<any[]> {
  try {
    const { getCurrentUser } = await import('./auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }
    
    const filterManagerId = managerId || currentUser.id
    const filters: any = { managerId: filterManagerId }
    
    if (status) {
      filters.status = status as 'pending' | 'approved' | 'rejected' | 'cancelled'
    }
    
    const requests = await db.getPTORequests(filters)
    
    return requests.map(request => ({
      id: request.id,
      employeeId: request.employee_id,
      employeeName: request.employee_name,
      managerId: request.manager_id,
      managerName: request.manager_name,
      requestType: request.request_type,
      startDate: request.start_date,
      endDate: request.end_date,
      daysRequested: request.days_requested,
      reason: request.reason,
      status: request.status,
      approvedBy: request.approved_by,
      approvedAt: request.approved_at,
      rejectedReason: request.rejected_reason,
      createdAt: request.created_at,
      updatedAt: request.updated_at
    }))
  } catch (error) {
    console.error('Failed to fetch PTO requests for manager:', error)
    return []
  }
}

export async function getPTORequestsForEmployee(employeeId?: string): Promise<any[]> {
  try {
    const { getCurrentUser } = await import('./auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }
    
    // If no employeeId provided, get current user's employee record
    let targetEmployeeId = employeeId
    if (!targetEmployeeId) {
      const employees = await getEmployees()
      const employee = employees.find(emp => emp.managerId === currentUser.id)
      if (!employee) {
        return []
      }
      targetEmployeeId = employee.id
    }
    
    const requests = await db.getPTORequests({ employeeId: targetEmployeeId })
    
    return requests.map(request => ({
      id: request.id,
      employeeId: request.employee_id,
      employeeName: request.employee_name,
      managerId: request.manager_id,
      managerName: request.manager_name,
      requestType: request.request_type,
      startDate: request.start_date,
      endDate: request.end_date,
      daysRequested: request.days_requested,
      reason: request.reason,
      status: request.status,
      approvedBy: request.approved_by,
      approvedAt: request.approved_at,
      rejectedReason: request.rejected_reason,
      createdAt: request.created_at,
      updatedAt: request.updated_at
    }))
  } catch (error) {
    console.error('Failed to fetch PTO requests for employee:', error)
    return []
  }
}

export async function getPTODashboardData(): Promise<any> {
  try {
    const { getCurrentUser, hasSuperAdminAccess } = await import('./auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return null
    }
    
    // Super-admins get a comprehensive view of all PTO data
    if (hasSuperAdminAccess(currentUser)) {
      // Super-admin view - show all PTO data across departments
      const allRequests = await db.getPTORequests({}) // Get all requests
      const allStats = await db.getPTOStats() // Get system-wide stats
      
      return {
        type: 'manager', // Use manager view for super-admin
        pendingRequests: allRequests.filter(r => r.status === 'pending'),
        teamRequests: allRequests,
        stats: {
          totalRequests: allStats.totalRequests,
          pendingRequests: allStats.pendingRequests,
          approvedRequests: allStats.approvedRequests,
          rejectedRequests: allStats.rejectedRequests
        }
      }
    }
    
    // Get employees managed by current user (for managers)
    const employees = await getEmployees()
    const managedEmployees = employees.filter(emp => emp.managerId === currentUser.id)
    
    // Check if user should get manager view (managers with employees, or admin roles)
    const shouldGetManagerView = (currentUser.role === 'manager' && managedEmployees.length > 0) ||
                                currentUser.role === 'hr-admin' ||
                                currentUser.role === 'admin'
    
    if (shouldGetManagerView) {
      // Manager/Admin view - show pending requests from their team or all requests for admins
      const pendingRequests = await getPTORequestsForManager(currentUser.id, 'pending')
      const allRequests = await getPTORequestsForManager(currentUser.id)
      const stats = await db.getPTOStats(currentUser.id)
      
      return {
        type: 'manager',
        pendingRequests,
        teamRequests: allRequests,
        stats: {
          totalRequests: stats.totalRequests,
          pendingRequests: stats.pendingRequests,
          approvedRequests: stats.approvedRequests,
          rejectedRequests: stats.rejectedRequests
        }
      }
    } else {
      // Employee view - show their own balance and requests
      const employee = employees.find(emp => emp.managerId === currentUser.id)
      if (!employee) {
        return null
      }
      
      const balance = await getPTOBalance(employee.id)
      const recentRequests = await getPTORequestsForEmployee(employee.id)
      
      return {
        type: 'employee',
        balance,
        recentRequests,
        stats: {
          totalRequests: recentRequests.length,
          pendingRequests: recentRequests.filter(r => r.status === 'pending').length,
          approvedRequests: recentRequests.filter(r => r.status === 'approved').length,
          rejectedRequests: recentRequests.filter(r => r.status === 'rejected').length
        }
      }
    }
  } catch (error) {
    console.error('Failed to fetch PTO dashboard data:', error)
    return null
  }
}

export async function getPTOStats(managerId?: string): Promise<any> {
  try {
    const { getCurrentUser } = await import('./auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return {
        totalRequests: 0,
        pendingRequests: 0,
        approvedRequests: 0,
        rejectedRequests: 0
      }
    }
    
    const filterManagerId = managerId || currentUser.id
    const stats = await db.getPTOStats(filterManagerId)
    
    return {
      totalRequests: stats.totalRequests,
      pendingRequests: stats.pendingRequests,
      approvedRequests: stats.approvedRequests,
      rejectedRequests: stats.rejectedRequests
    }
  } catch (error) {
    console.error('Failed to fetch PTO stats:', error)
    return {
      totalRequests: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      rejectedRequests: 0
    }
  }
}

export async function checkPTOAvailability(employeeId: string, daysRequested: number): Promise<boolean> {
  try {
    return await db.checkPTOAvailability(employeeId, daysRequested)
  } catch (error) {
    console.error('Failed to check PTO availability:', error)
    return false
  }
}

export async function initializeMarketingDepartmentPTO(): Promise<void> {
  try {
    // Get all employees in Marketing department
    const employees = await getEmployees()
    const marketingEmployees = employees.filter(emp => emp.departmentName === 'Marketing')
    const employeeIds = marketingEmployees.map(emp => emp.id)
    
    if (employeeIds.length > 0) {
      await db.initializePTOBalances(employeeIds, new Date().getFullYear(), 7)
      // console.log(`Initialized PTO balances for ${employeeIds.length} Marketing department employees`)
    }
  } catch (error) {
    console.error('Failed to initialize Marketing department PTO:', error)
    throw error
  }
}

// Cache invalidation functions for PTO
export function invalidatePTOCache(managerId?: string): void {
  // console.log('🗑️ Invalidating PTO cache')
  
  // Clear PTO-specific caches
  cache.delete('pto-dashboard-data')
  cache.delete('pto-stats')
  
  if (managerId) {
    cache.delete(`pto-manager-data-${managerId}`)
    cache.delete(`pto-stats-${managerId}`)
  } else {
    // Clear all PTO caches (brute force approach)
    cache.clear()
  }
}

// // console.log('📊 Database-backed data access functions loaded successfully')
